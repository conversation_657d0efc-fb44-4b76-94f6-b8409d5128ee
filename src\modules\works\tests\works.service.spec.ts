import { Test, TestingModule } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { WorksService } from "../works.service";
import { Work, WorkType, WorkStatus } from "../../../entities";
import { ConflictException, NotFoundException } from "@nestjs/common";

describe("WorksService", () => {
	let service: WorksService;
	let repository: Repository<Work>;

	const mockWork: Work = {
		id: 1,
		title: "Test Manga",
		type: WorkType.MANGA,
		author: "Test Author",
		status: WorkStatus.ONGOING,
		averageRating: 8.5,
		totalRatings: 100,
		createdAt: new Date(),
		updatedAt: new Date(),
		userWorks: [],
		notes: [],
	};

	const mockRepository = {
		findOne: jest.fn(),
		find: jest.fn(),
		create: jest.fn(),
		save: jest.fn(),
		remove: jest.fn(),
		update: jest.fn(),
		count: jest.fn(),
		createQueryBuilder: jest.fn(() => ({
			where: jest.fn().mockReturnThis(),
			andWhere: jest.fn().mockReturnThis(),
			orderBy: jest.fn().mockReturnThis(),
			addOrderBy: jest.fn().mockReturnThis(),
			skip: jest.fn().mockReturnThis(),
			take: jest.fn().mockReturnThis(),
			limit: jest.fn().mockReturnThis(),
			getManyAndCount: jest.fn(),
			getMany: jest.fn(),
			getRawMany: jest.fn(),
			getRawOne: jest.fn(),
			leftJoin: jest.fn().mockReturnThis(),
			select: jest.fn().mockReturnThis(),
			addSelect: jest.fn().mockReturnThis(),
			groupBy: jest.fn().mockReturnThis(),
			having: jest.fn().mockReturnThis(),
		})),
		query: jest.fn(),
	};

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			providers: [
				WorksService,
				{
					provide: getRepositoryToken(Work),
					useValue: mockRepository,
				},
			],
		}).compile();

		service = module.get<WorksService>(WorksService);
		repository = module.get<Repository<Work>>(getRepositoryToken(Work));
	});

	it("should be defined", () => {
		expect(service).toBeDefined();
	});

	describe("create", () => {
		it("should create a new work", async () => {
			const createWorkDto = {
				title: "New Manga",
				type: WorkType.MANGA,
				author: "New Author",
			};

			mockRepository.findOne.mockResolvedValue(null);
			mockRepository.create.mockReturnValue(mockWork);
			mockRepository.save.mockResolvedValue(mockWork);

			const result = await service.create(createWorkDto as any);

			expect(result).toEqual(mockWork);
			expect(mockRepository.findOne).toHaveBeenCalledWith({
				where: {
					title: createWorkDto.title,
					type: createWorkDto.type,
				},
			});
		});

		it("should throw ConflictException if work already exists", async () => {
			const createWorkDto = {
				title: "Existing Manga",
				type: WorkType.MANGA,
				author: "Author",
			};

			mockRepository.findOne.mockResolvedValue(mockWork);

			await expect(service.create(createWorkDto as any)).rejects.toThrow(ConflictException);
		});
	});

	describe("findOne", () => {
		it("should return a work if found", async () => {
			mockRepository.findOne.mockResolvedValue(mockWork);

			const result = await service.findOne(1);

			expect(result).toEqual(mockWork);
			expect(mockRepository.findOne).toHaveBeenCalledWith({
				where: { id: 1 },
				relations: ["userWorks", "notes"],
			});
		});

		it("should throw NotFoundException if work not found", async () => {
			mockRepository.findOne.mockResolvedValue(null);

			await expect(service.findOne(999)).rejects.toThrow(NotFoundException);
		});
	});

	describe("getPopular", () => {
		it("should return popular works", async () => {
			const popularWorks = [mockWork];
			mockRepository.find.mockResolvedValue(popularWorks);

			const result = await service.getPopular(10);

			expect(result).toEqual(popularWorks);
			expect(mockRepository.find).toHaveBeenCalledWith({
				order: {
					averageRating: "DESC",
					totalRatings: "DESC",
				},
				take: 10,
			});
		});
	});

	describe("getRandomWorks", () => {
		it("should return random works", async () => {
			const randomWorks = [mockWork];
			const mockQueryBuilder = mockRepository.createQueryBuilder();
			mockQueryBuilder.getMany.mockResolvedValue(randomWorks);

			const result = await service.getRandomWorks(5);

			expect(result).toEqual(randomWorks);
			expect(mockRepository.createQueryBuilder).toHaveBeenCalledWith("work");
		});
	});

	describe("updateRating", () => {
		it("should update work rating", async () => {
			const mockQueryBuilder = mockRepository.createQueryBuilder();
			mockQueryBuilder.getRawOne.mockResolvedValue({
				averageRating: "8.5",
				totalRatings: "100",
			});

			await service.updateRating(1);

			expect(mockRepository.update).toHaveBeenCalledWith(1, {
				averageRating: 8.5,
				totalRatings: 100,
			});
		});
	});
});
