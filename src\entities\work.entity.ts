import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, OneToMany, Index } from "typeorm";
import { ApiProperty } from "@nestjs/swagger";
import { Note, UserWork } from ".";

export enum WorkType {
	MANGA = "manga",
	MANHWA = "manhwa",
	MANHUA = "manhua",
}

export enum WorkStatus {
	ONGOING = "ongoing",
	COMPLETED = "completed",
	HIATUS = "hiatus",
	CANCELLED = "cancelled",
}

@Entity("works")
@Index(["title", "type"])
export class Work {
	@ApiProperty({
		description: "ID único da obra",
		example: 1,
	})
	@PrimaryGeneratedColumn()
	id: number;

	@ApiProperty({
		description: "Título da obra",
		example: "One Piece",
	})
	@Column()
	title: string;

	@ApiProperty({
		description: "Título alternativo",
		example: "ワンピース",
	})
	@Column({ nullable: true })
	alternativeTitle?: string;

	@ApiProperty({
		description: "Tipo da obra",
		enum: WorkType,
		example: WorkType.MANGA,
	})
	@Column({
		type: "enum",
		enum: WorkType,
	})
	type: WorkType;

	@ApiProperty({
		description: "Autor(es) da obra",
		example: "Eiichiro Oda",
	})
	@Column()
	author: string;

	@ApiProperty({
		description: "Artista(s) da obra",
		example: "Eiichiro Oda",
	})
	@Column({ nullable: true })
	artist?: string;

	@ApiProperty({
		description: "Sinopse da obra",
		example: "A história de Monkey D. Luffy...",
	})
	@Column({ type: "text", nullable: true })
	synopsis?: string;

	@ApiProperty({
		description: "Gêneros da obra",
		example: ["Ação", "Aventura", "Comédia"],
		type: [String],
	})
	@Column("simple-array", { nullable: true })
	genres?: string[];

	@ApiProperty({
		description: "Status da obra",
		enum: WorkStatus,
		example: WorkStatus.ONGOING,
	})
	@Column({
		type: "enum",
		enum: WorkStatus,
		default: WorkStatus.ONGOING,
	})
	status: WorkStatus;

	@ApiProperty({
		description: "Ano de publicação",
		example: 1997,
	})
	@Column({ nullable: true })
	publicationYear?: number;

	@ApiProperty({
		description: "Total de capítulos",
		example: 1000,
	})
	@Column({ nullable: true })
	totalChapters?: number;

	@ApiProperty({
		description: "URL da capa",
		example: "https://example.com/cover.jpg",
	})
	@Column({ nullable: true })
	coverImage?: string;

	@ApiProperty({
		description: "Nota média da obra",
		example: 9.5,
	})
	@Column({ type: "decimal", precision: 3, scale: 2, default: 0 })
	averageRating: number;

	@ApiProperty({
		description: "Total de avaliações",
		example: 1500,
	})
	@Column({ default: 0 })
	totalRatings: number;

	@ApiProperty({
		description: "Data de criação",
	})
	@CreateDateColumn()
	createdAt: Date;

	@ApiProperty({
		description: "Data de última atualização",
	})
	@UpdateDateColumn()
	updatedAt: Date;

	// Relacionamentos
	@OneToMany(() => UserWork, userWork => userWork.work)
	userWorks: UserWork[];

	@OneToMany(() => Note, note => note.work)
	notes: Note[];
}
