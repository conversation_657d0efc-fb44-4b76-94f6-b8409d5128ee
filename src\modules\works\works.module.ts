import { <PERSON><PERSON><PERSON>, MiddlewareConsumer, RequestMethod } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { WorksService } from "./works.service";
import { WorksController } from "./works.controller";
import { Work } from "../../entities";
import { SimpleCacheMiddleware } from "./middleware/cache.middleware";

@Module({
	imports: [TypeOrmModule.forFeature([Work])],
	controllers: [WorksController],
	providers: [WorksService, SimpleCacheMiddleware],
	exports: [WorksService],
})
export class WorksModule {
	configure(consumer: MiddlewareConsumer) {
		consumer
			.apply(SimpleCacheMiddleware)
			.forRoutes(
				{ path: "works/popular", method: RequestMethod.GET },
				{ path: "works/random", method: RequestMethod.GET },
				{ path: "works/statistics", method: RequestMethod.GET },
				{ path: "works/genre/*", method: RequestMethod.GET }
			);
	}
}
